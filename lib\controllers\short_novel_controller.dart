import 'dart:convert';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:novel_app/models/short_novel_memory.dart';
import 'package:novel_app/models/novel.dart';
// import 'package:novel_app/models/chapter.dart' as ChapterModel; // 不需要单独的Chapter模型
import 'package:novel_app/services/ai_service.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:uuid/uuid.dart';

class ShortNovelController extends GetxController {
  final AIService _aiService = Get.find<AIService>();
  final NovelController _novelController = Get.find<NovelController>();
  final GetStorage _storage = GetStorage();
  
  // 当前生成状态
  final Rx<ShortNovelGenerationStage> currentStage = ShortNovelGenerationStage.input.obs;
  final Rx<ShortNovelMemory?> memory = Rx<ShortNovelMemory?>(null);
  final RxBool isGenerating = false.obs;
  final RxString generationStatus = ''.obs;
  final RxString realtimeOutput = ''.obs;
  final RxDouble progress = 0.0.obs;
  
  // 编辑状态
  final RxString editableWorldView = ''.obs;
  final RxString editableCharacterArcs = ''.obs;
  final RxString editableOutline = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _tryRestoreProgress();
  }

  /// 开始短篇小说生成流程
  Future<void> startGeneration({
    required String title,
    required List<String> genres,
    required String background,
    required String otherRequirements,
    required String targetReader,
    required int targetWordCount,
    required List<String> characterCards,
    required List<String> characterTypes,
  }) async {
    try {
      // 创建新的记忆对象
      final conversationId = 'short_novel_${const Uuid().v4()}';
      memory.value = ShortNovelMemory(
        title: title,
        genres: genres,
        background: background,
        otherRequirements: otherRequirements,
        targetReader: targetReader,
        targetWordCount: targetWordCount,
        conversationId: conversationId,
      );

      // 保存基本信息到NovelController
      _novelController.title.value = title;
      _novelController.selectedGenres.value = genres;
      _novelController.background.value = background;
      _novelController.otherRequirements.value = otherRequirements;
      _novelController.targetReader.value = targetReader;
      _novelController.shortNovelWordCount.value = 
          ShortNovelWordCount.values.firstWhere(
            (w) => w.count == targetWordCount,
            orElse: () => ShortNovelWordCount.words10000,
          );

      // 开始生成世界观
      await generateWorldView();
    } catch (e) {
      _handleError('开始生成失败', e);
    }
  }

  /// 生成世界观
  Future<void> generateWorldView() async {
    if (memory.value == null) return;

    try {
      currentStage.value = ShortNovelGenerationStage.generatingWorldView;
      isGenerating.value = true;
      generationStatus.value = '正在生成世界观...';
      realtimeOutput.value = '';

      final buffer = StringBuffer();
      await for (final chunk in _aiService.generateShortNovelWorldView(
        title: memory.value!.title,
        genres: memory.value!.genres,
        background: memory.value!.background,
        otherRequirements: memory.value!.otherRequirements,
        targetReader: memory.value!.targetReader,
        targetWordCount: memory.value!.targetWordCount,
        conversationId: memory.value!.conversationId,
      )) {
        buffer.write(chunk);
        realtimeOutput.value = buffer.toString();
      }

      // 更新记忆
      memory.value = memory.value!.copyWith(worldView: buffer.toString());
      editableWorldView.value = buffer.toString();
      
      // 保存进度
      await _saveProgress();
      
      // 切换到编辑世界观阶段
      currentStage.value = ShortNovelGenerationStage.editingWorldView;
      generationStatus.value = '世界观生成完成，请检查并编辑';
      
    } catch (e) {
      _handleError('生成世界观失败', e);
    } finally {
      isGenerating.value = false;
    }
  }

  /// 确认世界观并生成角色脉络
  Future<void> confirmWorldViewAndGenerateCharacters() async {
    if (memory.value == null) return;

    try {
      // 更新世界观
      memory.value = memory.value!.copyWith(worldView: editableWorldView.value);
      await _saveProgress();

      currentStage.value = ShortNovelGenerationStage.generatingCharacterArcs;
      isGenerating.value = true;
      generationStatus.value = '正在生成角色发展脉络...';
      realtimeOutput.value = '';

      final buffer = StringBuffer();
      await for (final chunk in _aiService.generateShortNovelCharacterArcs(
        title: memory.value!.title,
        worldView: memory.value!.worldView,
        characterCards: _getSelectedCharacterCardNames(),
        characterTypes: _novelController.selectedCharacterTypes.map((t) => t.name).toList(),
        background: memory.value!.background,
        targetWordCount: memory.value!.targetWordCount,
        conversationId: memory.value!.conversationId,
      )) {
        buffer.write(chunk);
        realtimeOutput.value = buffer.toString();
      }

      // 更新记忆
      memory.value = memory.value!.copyWith(characterArcs: buffer.toString());
      editableCharacterArcs.value = buffer.toString();
      
      // 保存进度
      await _saveProgress();
      
      // 切换到编辑角色脉络阶段
      currentStage.value = ShortNovelGenerationStage.editingCharacterArcs;
      generationStatus.value = '角色发展脉络生成完成，请检查并编辑';
      
    } catch (e) {
      _handleError('生成角色发展脉络失败', e);
    } finally {
      isGenerating.value = false;
    }
  }

  /// 确认角色脉络并生成大纲
  Future<void> confirmCharacterArcsAndGenerateOutline() async {
    if (memory.value == null) return;

    try {
      // 更新角色脉络
      memory.value = memory.value!.copyWith(characterArcs: editableCharacterArcs.value);
      await _saveProgress();

      currentStage.value = ShortNovelGenerationStage.generatingOutline;
      isGenerating.value = true;
      generationStatus.value = '正在生成详细大纲...';
      realtimeOutput.value = '';

      final buffer = StringBuffer();
      await for (final chunk in _aiService.generateShortNovelDetailedOutline(
        title: memory.value!.title,
        worldView: memory.value!.worldView,
        characterArcs: memory.value!.characterArcs,
        background: memory.value!.background,
        otherRequirements: memory.value!.otherRequirements,
        targetWordCount: memory.value!.targetWordCount,
        conversationId: memory.value!.conversationId,
      )) {
        buffer.write(chunk);
        realtimeOutput.value = buffer.toString();
      }

      // 更新记忆
      memory.value = memory.value!.copyWith(detailedOutline: buffer.toString());
      editableOutline.value = buffer.toString();
      
      // 保存进度
      await _saveProgress();
      
      // 切换到编辑大纲阶段
      currentStage.value = ShortNovelGenerationStage.editingOutline;
      generationStatus.value = '详细大纲生成完成，请检查并编辑';
      
    } catch (e) {
      _handleError('生成详细大纲失败', e);
    } finally {
      isGenerating.value = false;
    }
  }

  /// 确认大纲并开始生成内容
  Future<void> confirmOutlineAndStartGeneration() async {
    if (memory.value == null) return;

    try {
      // 更新大纲
      memory.value = memory.value!.copyWith(detailedOutline: editableOutline.value);
      await _saveProgress();

      currentStage.value = ShortNovelGenerationStage.generatingContent;
      isGenerating.value = true;
      generationStatus.value = '开始生成小说内容...';

      // 开始分段生成
      await _generateAllParts();
      
    } catch (e) {
      _handleError('生成小说内容失败', e);
    }
  }

  /// 分段生成所有内容
  Future<void> _generateAllParts() async {
    if (memory.value == null) return;

    final totalParts = memory.value!.totalPartsCount;
    
    for (int i = memory.value!.currentPartIndex; i < totalParts; i++) {
      try {
        generationStatus.value = '正在生成第${i + 1}部分（共$totalParts部分）...';
        progress.value = i / totalParts;
        
        await _generatePart(i);
        
        // 保存进度
        await _saveProgress();
        
      } catch (e) {
        _handleError('生成第${i + 1}部分失败', e);
        return;
      }
    }

    // 生成完成
    await _completeGeneration();
  }

  /// 生成单个部分
  Future<void> _generatePart(int partIndex) async {
    if (memory.value == null) return;

    final partPrompt = '''
${memory.value!.getCurrentContext()}

【当前任务】
生成第${partIndex + 1}部分内容，约3000字

【生成要求】
1. 严格按照大纲执行
2. 保持与前文的连贯性
3. 推进角色发展和情节
4. 字数控制在2800-3200字之间
5. 直接输出小说正文，无需标题

请开始生成：
''';

    final buffer = StringBuffer();
    await for (final chunk in _aiService.generateContentStream(
      systemPrompt: '''
你是专业的短篇小说创作助手，擅长保持故事连贯性和角色一致性。
请根据提供的世界观、角色脉络和大纲，创作高质量的小说内容。
''',
      userPrompt: partPrompt,
      maxTokens: 8000,
      temperature: 0.75,
      conversationId: memory.value!.conversationId,
    )) {
      buffer.write(chunk);
      realtimeOutput.value = buffer.toString();
    }

    // 添加生成的部分
    memory.value!.addGeneratedPart(buffer.toString());
  }

  /// 完成生成
  Future<void> _completeGeneration() async {
    if (memory.value == null) return;

    try {
      currentStage.value = ShortNovelGenerationStage.completed;
      isGenerating.value = false;
      generationStatus.value = '短篇小说生成完成！';
      progress.value = 1.0;

      // 保存为小说
      await _saveAsNovel();
      
      // 清除进度
      await _clearProgress();
      
      Get.snackbar('成功', '短篇小说《${memory.value!.title}》生成完成！');
      
    } catch (e) {
      _handleError('保存小说失败', e);
    }
  }

  /// 保存为小说
  Future<void> _saveAsNovel() async {
    if (memory.value == null) return;

    // 创建第0章：元数据
    final metadataChapter = Chapter(
      number: 0,
      title: '世界观与大纲',
      content: jsonEncode({
        'worldView': memory.value!.worldView,
        'characterArcs': memory.value!.characterArcs,
        'detailedOutline': memory.value!.detailedOutline,
        'targetWordCount': memory.value!.targetWordCount,
        'generationTimestamp': DateTime.now().toIso8601String(),
      }),
    );

    // 创建第1章：小说内容
    final contentChapter = Chapter(
      number: 1,
      title: '短篇小说正文',
      content: memory.value!.getFullContent(),
    );

    // 创建小说对象
    final novel = Novel(
      title: memory.value!.title,
      genre: memory.value!.genres.join(','),
      outline: memory.value!.detailedOutline,
      content: memory.value!.getFullContent(),
      chapters: [metadataChapter, contentChapter],
      createdAt: DateTime.now(),
      style: _novelController.style.value,
      sessionId: memory.value!.conversationId,
    );

    // 保存小说
    await _novelController.saveNovel(novel);
  }

  /// 保存进度
  Future<void> _saveProgress() async {
    if (memory.value != null) {
      await _storage.write('short_novel_progress', memory.value!.toJson());
    }
  }

  /// 恢复进度
  Future<void> _tryRestoreProgress() async {
    try {
      final progressData = _storage.read('short_novel_progress');
      if (progressData != null) {
        memory.value = ShortNovelMemory.fromJson(progressData);
        // 根据记忆状态设置当前阶段
        _determineCurrentStage();
      }
    } catch (e) {
      print('恢复进度失败: $e');
    }
  }

  /// 根据记忆状态确定当前阶段
  void _determineCurrentStage() {
    if (memory.value == null) return;

    if (memory.value!.isCompleted) {
      currentStage.value = ShortNovelGenerationStage.completed;
    } else if (memory.value!.generatedParts.isNotEmpty) {
      currentStage.value = ShortNovelGenerationStage.generatingContent;
    } else if (memory.value!.detailedOutline.isNotEmpty) {
      currentStage.value = ShortNovelGenerationStage.editingOutline;
      editableOutline.value = memory.value!.detailedOutline;
    } else if (memory.value!.characterArcs.isNotEmpty) {
      currentStage.value = ShortNovelGenerationStage.editingCharacterArcs;
      editableCharacterArcs.value = memory.value!.characterArcs;
    } else if (memory.value!.worldView.isNotEmpty) {
      currentStage.value = ShortNovelGenerationStage.editingWorldView;
      editableWorldView.value = memory.value!.worldView;
    }
  }

  /// 清除进度
  Future<void> _clearProgress() async {
    await _storage.remove('short_novel_progress');
  }

  /// 处理错误
  void _handleError(String message, dynamic error) {
    currentStage.value = ShortNovelGenerationStage.error;
    isGenerating.value = false;
    generationStatus.value = '$message: $error';
    Get.snackbar('错误', '$message: $error');
  }

  /// 重新生成当前步骤
  Future<void> regenerateCurrentStep() async {
    switch (currentStage.value) {
      case ShortNovelGenerationStage.editingWorldView:
        await generateWorldView();
        break;
      case ShortNovelGenerationStage.editingCharacterArcs:
        await confirmWorldViewAndGenerateCharacters();
        break;
      case ShortNovelGenerationStage.editingOutline:
        await confirmCharacterArcsAndGenerateOutline();
        break;
      default:
        break;
    }
  }

  /// 重置生成流程
  void resetGeneration() {
    memory.value = null;
    currentStage.value = ShortNovelGenerationStage.input;
    isGenerating.value = false;
    generationStatus.value = '';
    realtimeOutput.value = '';
    progress.value = 0.0;
    editableWorldView.value = '';
    editableCharacterArcs.value = '';
    editableOutline.value = '';
    _clearProgress();
  }

  /// 获取选中的角色卡片名称列表
  List<String> _getSelectedCharacterCardNames() {
    final List<String> names = [];
    for (final entry in _novelController.selectedCharacterCards.entries) {
      for (final card in entry.value) {
        names.add(card.name);
      }
    }
    return names;
  }
}
