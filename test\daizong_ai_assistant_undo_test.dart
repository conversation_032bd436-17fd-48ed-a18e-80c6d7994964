import 'package:flutter_test/flutter_test.dart';
import '../lib/models/novel.dart';

void main() {
  group('岱宗AI辅助助手撤销和取消功能测试', () {
    late Novel testNovel;

    setUp(() {
      // 创建测试小说
      testNovel = Novel(
        title: '测试小说',
        genre: '测试类型',
        outline: '测试大纲',
        content: '测试内容',
        chapters: [
          Chapter(
            number: 1,
            title: '第一章',
            content: '第一章的原始内容',
          ),
          Chapter(
            number: 2,
            title: '第二章',
            content: '第二章的原始内容',
          ),
        ],
        createdAt: DateTime.now(),
      );
    });

    test('测试小说副本创建功能', () {
      // 测试小说数据结构
      expect(testNovel.chapters.length, equals(2));
      expect(testNovel.chapters[0].title, equals('第一章'));
      expect(testNovel.chapters[0].content, equals('第一章的原始内容'));

      // 测试copyWith方法
      final copiedNovel = testNovel.copyWith(
        chapters: testNovel.chapters.map((chapter) => chapter.copyWith()).toList(),
      );

      // 验证副本是不同的实例
      expect(copiedNovel.id, equals(testNovel.id));
      expect(copiedNovel.title, equals(testNovel.title));
      expect(copiedNovel.chapters.length, equals(testNovel.chapters.length));

      // 验证章节也是不同的实例
      expect(copiedNovel.chapters[0].title, equals(testNovel.chapters[0].title));
      expect(copiedNovel.chapters[0].content, equals(testNovel.chapters[0].content));

      // 修改副本不应该影响原始数据
      copiedNovel.chapters[0].content = '修改后的内容';
      expect(testNovel.chapters[0].content, equals('第一章的原始内容'));
    });
  });
}
