import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // 定义岱宗AI主题色 - 自然雅致风格
  static const Color deepEmerald = Color(0xFF2D5016);     // 深翠绿 - 主色调
  static const Color inkGreen = Color(0xFF1B4332);        // 墨绿 - 主色调
  static const Color champagneGold = Color(0xFFF7E7CE);   // 香槟金 - 辅助色
  static const Color ivoryWhite = Color(0xFFFFFDD0);      // 象牙白 - 辅助色
  static const Color amberYellow = Color(0xFFFFBF00);     // 琥珀黄 - 强调色

  // 保持兼容性的别名
  static const Color forestGreen = deepEmerald;
  static const Color earthYellow = champagneGold;
  static const Color lightGreen = Color(0xFF4F7942);      // 竹绿 - 中间色调
  static const Color lightYellow = ivoryWhite;

  static ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置亮色模式下的背景色为更柔和的颜色
    scaffoldBackgroundColor: const Color(0xFFF8F8F8),
    colorScheme: ColorScheme.fromSeed(
      seedColor: forestGreen,
      brightness: Brightness.light,
      primary: forestGreen,
      secondary: lightGreen,
      tertiary: earthYellow,
      surface: lightYellow.withAlpha(51),
      // 提高亮色模式下的文字对比度
      onSurface: const Color(0xFF333333), // 深灰色文字，提高对比度
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: forestGreen,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: forestGreen,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: lightYellow.withAlpha(77),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: Colors.grey),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: forestGreen),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(Colors.white),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: const TextStyle(
        color: Color(0xFF333333),
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: Colors.white,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        color: Color(0xFF333333),
        fontSize: 16,
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(58, 107, 53, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
  );

  static ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    fontFamily: GoogleFonts.notoSerif().fontFamily,
    // 设置深色模式下的背景色为更浅的颜色，提高可读性
    scaffoldBackgroundColor: const Color(0xFF1A1A1A),
    colorScheme: ColorScheme.fromSeed(
      seedColor: forestGreen,
      brightness: Brightness.dark,
      primary: forestGreen,
      secondary: lightGreen,
      tertiary: earthYellow,
      // 提高深色模式下的对比度
      surface: const Color(0xFF1E1E1E),
      onSurface: const Color(0xFFF5F5F5), // 浅灰色文字，提高对比度
    ),
    appBarTheme: const AppBarTheme(
      backgroundColor: forestGreen,
      foregroundColor: Colors.white,
      elevation: 0,
    ),
    cardTheme: CardTheme(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      clipBehavior: Clip.antiAlias,
    ),
    listTileTheme: ListTileThemeData(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: forestGreen,
        foregroundColor: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: Colors.grey.shade800,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(8),
        borderSide: const BorderSide(color: lightGreen),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    ),
    // 添加下拉菜单主题
    dropdownMenuTheme: DropdownMenuThemeData(
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(const Color(0xFF2A2A2A)),
        elevation: WidgetStateProperty.all(8),
        shape: WidgetStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        padding: WidgetStateProperty.all(
          const EdgeInsets.symmetric(vertical: 8),
        ),
      ),
      textStyle: const TextStyle(
        color: Color(0xFFF5F5F5),
        fontSize: 16,
      ),
    ),
    // 添加弹出菜单主题
    popupMenuTheme: PopupMenuThemeData(
      color: const Color(0xFF2A2A2A),
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      textStyle: const TextStyle(
        color: Color(0xFFF5F5F5),
        fontSize: 16,
      ),
    ),
    // 添加页面过渡动画
    pageTransitionsTheme: const PageTransitionsTheme(
      builders: {
        TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
      },
    ),
    // 添加滚动物理效果
    scrollbarTheme: ScrollbarThemeData(
      thickness: WidgetStateProperty.all(6),
      thumbColor:
          WidgetStateProperty.all(const Color.fromRGBO(147, 184, 132, 0.5)),
      radius: const Radius.circular(3),
      crossAxisMargin: 2,
      mainAxisMargin: 2,
    ),
  );
}
