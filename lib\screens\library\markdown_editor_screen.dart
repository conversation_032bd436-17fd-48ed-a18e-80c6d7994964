import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:io';
import 'package:path/path.dart' as path;

/// Markdown文件编辑器
class MarkdownEditorScreen extends StatefulWidget {
  final File file;

  const MarkdownEditorScreen({
    super.key,
    required this.file,
  });

  @override
  State<MarkdownEditorScreen> createState() => _MarkdownEditorScreenState();
}

class _MarkdownEditorScreenState extends State<MarkdownEditorScreen> {
  late TextEditingController _controller;
  bool _isLoading = true;
  bool _hasChanges = false;
  String _originalContent = '';

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _loadFileContent();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadFileContent() async {
    try {
      if (await widget.file.exists()) {
        final content = await widget.file.readAsString();
        _originalContent = content;
        _controller.text = content;
      }
    } catch (e) {
      Get.snackbar('错误', '加载文件失败：$e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _saveFile() async {
    try {
      await widget.file.writeAsString(_controller.text);
      _originalContent = _controller.text;
      setState(() {
        _hasChanges = false;
      });
      Get.snackbar('成功', '文件已保存');
    } catch (e) {
      Get.snackbar('错误', '保存失败：$e');
    }
  }

  void _onTextChanged() {
    final hasChanges = _controller.text != _originalContent;
    if (hasChanges != _hasChanges) {
      setState(() {
        _hasChanges = hasChanges;
      });
    }
  }

  Future<bool> _onWillPop() async {
    if (!_hasChanges) return true;

    final shouldSave = await Get.dialog<bool>(
      AlertDialog(
        title: const Text('保存更改'),
        content: const Text('文件已修改，是否保存更改？'),
        actions: [
          TextButton(
            onPressed: () => Get.back(result: false),
            child: const Text('不保存'),
          ),
          TextButton(
            onPressed: () => Get.back(result: null),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Get.back(result: true),
            child: const Text('保存'),
          ),
        ],
      ),
    );

    if (shouldSave == true) {
      await _saveFile();
      return true;
    } else if (shouldSave == false) {
      return true;
    } else {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final fileName = path.basename(widget.file.path);

    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        appBar: AppBar(
          title: Text(fileName),
          actions: [
            if (_hasChanges)
              Container(
                margin: const EdgeInsets.only(right: 8),
                child: const Icon(
                  Icons.circle,
                  color: Colors.orange,
                  size: 12,
                ),
              ),
            IconButton(
              icon: const Icon(Icons.save),
              tooltip: '保存',
              onPressed: _hasChanges ? _saveFile : null,
            ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'word_count':
                    _showWordCount();
                    break;
                  case 'find_replace':
                    _showFindReplace();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'word_count',
                  child: Row(
                    children: [
                      Icon(Icons.text_fields),
                      SizedBox(width: 8),
                      Text('字数统计'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'find_replace',
                  child: Row(
                    children: [
                      Icon(Icons.find_replace),
                      SizedBox(width: 8),
                      Text('查找替换'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Padding(
                padding: const EdgeInsets.all(16),
                child: TextField(
                  controller: _controller,
                  onChanged: (_) => _onTextChanged(),
                  maxLines: null,
                  expands: true,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: '在这里开始写作...',
                  ),
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                ),
              ),
      ),
    );
  }

  void _showWordCount() {
    final text = _controller.text;
    final wordCount = text.replaceAll(RegExp(r'\s'), '').length;
    final lineCount = text.split('\n').length;
    final charCount = text.length;

    Get.dialog(
      AlertDialog(
        title: const Text('字数统计'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('字数：$wordCount'),
            Text('字符数：$charCount'),
            Text('行数：$lineCount'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showFindReplace() {
    final findController = TextEditingController();
    final replaceController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('查找替换'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: findController,
              decoration: const InputDecoration(
                labelText: '查找',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: replaceController,
              decoration: const InputDecoration(
                labelText: '替换为',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              final findText = findController.text;
              final replaceText = replaceController.text;
              if (findText.isNotEmpty) {
                final newText = _controller.text.replaceAll(findText, replaceText);
                _controller.text = newText;
                _onTextChanged();
              }
              Get.back();
            },
            child: const Text('替换全部'),
          ),
        ],
      ),
    );
  }
}
