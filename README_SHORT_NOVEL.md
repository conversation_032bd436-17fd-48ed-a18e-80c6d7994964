# 短篇小说生成功能说明

## 🎯 功能概述

全新的短篇小说生成功能采用分步骤生成流程，确保生成的短篇小说具有完整的世界观、连贯的角色发展和精彩的故事情节。

## 📋 生成流程

### 第一步：用户输入
- 小说标题
- 目标字数（3000-20000字）
- 故事背景
- 其他特殊要求

### 第二步：生成世界观
- 系统根据用户输入自动生成完整的世界观设定
- 包括时代背景、地理环境、社会制度、文化背景等
- **用户可以编辑和修改生成的世界观**

### 第三步：生成角色发展脉络
- 基于确认的世界观生成角色体系
- 包括主要角色设定、角色关系网络、发展轨迹等
- **用户可以编辑和修改角色设定**

### 第四步：生成详细大纲
- 基于世界观和角色设定生成完整的故事大纲
- 按照目标字数自动分段（每段约3000字）
- 包含每段的情节目标、角色发展、关键场景等
- **用户可以编辑和修改大纲**

### 第五步：分段生成内容
- 根据确认的大纲分段生成小说内容
- 每段约3000字，使用langchain memory保持连贯性
- 实时显示生成进度和内容

## 💾 数据存储

### 第0章：元数据
存储以下信息：
- 世界观设定
- 角色发展脉络
- 详细大纲
- 生成时间戳

### 第1章：小说正文
存储完整的短篇小说内容

## 🔧 技术特点

### 连贯性保证
- 使用langchain memory机制
- 每次生成都传递完整的上下文信息
- 包括世界观、角色脉络、大纲和已生成内容

### 用户控制
- 每个关键步骤都支持用户编辑
- 可以重新生成任何步骤
- 支持中断和恢复生成

### 智能分段
- 根据目标字数自动计算段落数
- 每段3000字左右，符合AI生成能力
- 按照故事结构合理分配字数

## 🎨 用户界面

### 分阶段界面
- 输入阶段：收集用户需求
- 编辑阶段：允许用户修改生成内容
- 生成阶段：显示实时进度和内容
- 完成阶段：展示最终结果

### 进度保存
- 自动保存生成进度
- 支持中断后恢复
- 错误处理和重试机制

## 🚀 使用方法

1. 在首页选择"短篇小说"模式
2. 填写基本信息（标题、字数、背景等）
3. 点击"开始生成"
4. 依次确认世界观、角色脉络、详细大纲
5. 等待分段生成完成
6. 查看生成的完整短篇小说

## 📝 注意事项

- 生成过程可能需要较长时间，请耐心等待
- 建议在网络稳定的环境下使用
- 可以随时中断生成，进度会自动保存
- 每个编辑阶段都可以重新生成内容

## 🔄 与长篇小说的区别

| 特性 | 短篇小说 | 长篇小说 |
|------|----------|----------|
| 生成方式 | 分步骤生成 | 大纲+章节生成 |
| 用户控制 | 每步可编辑 | 大纲可编辑 |
| 字数范围 | 3000-20000字 | 无限制 |
| 数据存储 | 2章（元数据+正文） | 多章节 |
| 连贯性 | Memory机制 | 对话历史 |

## 🎯 适用场景

- 创作短篇小说作品
- 快速原型验证故事创意
- 学习小说创作结构
- 生成完整的故事样本

这个新的短篇小说功能将为用户提供更加精细化和可控的创作体验！
