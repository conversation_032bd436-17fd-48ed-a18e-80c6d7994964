import 'dart:convert';

/// 短篇小说生成过程中的记忆数据
class ShortNovelMemory {
  final String title;
  final List<String> genres;
  final String background;
  final String otherRequirements;
  final String targetReader;
  final int targetWordCount;
  
  // 生成的内容
  String worldView;
  String characterArcs;
  String detailedOutline;
  List<String> generatedParts;
  
  // 生成状态
  int currentPartIndex;
  String conversationId;
  DateTime createdAt;
  DateTime lastUpdated;

  ShortNovelMemory({
    required this.title,
    required this.genres,
    required this.background,
    required this.otherRequirements,
    required this.targetReader,
    required this.targetWordCount,
    this.worldView = '',
    this.characterArcs = '',
    this.detailedOutline = '',
    this.generatedParts = const [],
    this.currentPartIndex = 0,
    required this.conversationId,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) : createdAt = createdAt ?? DateTime.now(),
       lastUpdated = lastUpdated ?? DateTime.now();

  /// 获取当前上下文（用于传递给AI）
  String getCurrentContext() {
    final buffer = StringBuffer();
    
    if (worldView.isNotEmpty) {
      buffer.writeln('【世界观设定】');
      buffer.writeln(worldView);
      buffer.writeln();
    }
    
    if (characterArcs.isNotEmpty) {
      buffer.writeln('【角色发展脉络】');
      buffer.writeln(characterArcs);
      buffer.writeln();
    }
    
    if (detailedOutline.isNotEmpty) {
      buffer.writeln('【故事大纲】');
      buffer.writeln(detailedOutline);
      buffer.writeln();
    }
    
    if (generatedParts.isNotEmpty) {
      buffer.writeln('【已生成内容】');
      buffer.writeln(generatedParts.join('\n\n'));
      buffer.writeln();
    }
    
    buffer.writeln('【当前进度】');
    buffer.writeln('正在生成第${currentPartIndex + 1}部分，目标字数约3000字');
    
    return buffer.toString();
  }

  /// 获取世界观和角色上下文
  String getWorldAndCharacterContext() {
    final buffer = StringBuffer();
    
    if (worldView.isNotEmpty) {
      buffer.writeln('【世界观设定】');
      buffer.writeln(worldView);
      buffer.writeln();
    }
    
    if (characterArcs.isNotEmpty) {
      buffer.writeln('【角色发展脉络】');
      buffer.writeln(characterArcs);
      buffer.writeln();
    }
    
    return buffer.toString();
  }

  /// 计算需要的部分数量
  int get totalPartsCount => (targetWordCount / 3000).ceil();

  /// 检查是否完成生成
  bool get isCompleted => generatedParts.length >= totalPartsCount;

  /// 获取当前生成进度（0.0 - 1.0）
  double get progress {
    if (totalPartsCount == 0) return 0.0;
    return generatedParts.length / totalPartsCount;
  }

  /// 添加生成的部分
  void addGeneratedPart(String content) {
    generatedParts = [...generatedParts, content];
    currentPartIndex = generatedParts.length;
    lastUpdated = DateTime.now();
  }

  /// 获取完整的小说内容
  String getFullContent() {
    return generatedParts.join('\n\n');
  }

  /// 复制并更新
  ShortNovelMemory copyWith({
    String? title,
    List<String>? genres,
    String? background,
    String? otherRequirements,
    String? targetReader,
    int? targetWordCount,
    String? worldView,
    String? characterArcs,
    String? detailedOutline,
    List<String>? generatedParts,
    int? currentPartIndex,
    String? conversationId,
    DateTime? createdAt,
    DateTime? lastUpdated,
  }) {
    return ShortNovelMemory(
      title: title ?? this.title,
      genres: genres ?? this.genres,
      background: background ?? this.background,
      otherRequirements: otherRequirements ?? this.otherRequirements,
      targetReader: targetReader ?? this.targetReader,
      targetWordCount: targetWordCount ?? this.targetWordCount,
      worldView: worldView ?? this.worldView,
      characterArcs: characterArcs ?? this.characterArcs,
      detailedOutline: detailedOutline ?? this.detailedOutline,
      generatedParts: generatedParts ?? this.generatedParts,
      currentPartIndex: currentPartIndex ?? this.currentPartIndex,
      conversationId: conversationId ?? this.conversationId,
      createdAt: createdAt ?? this.createdAt,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'genres': genres,
      'background': background,
      'otherRequirements': otherRequirements,
      'targetReader': targetReader,
      'targetWordCount': targetWordCount,
      'worldView': worldView,
      'characterArcs': characterArcs,
      'detailedOutline': detailedOutline,
      'generatedParts': generatedParts,
      'currentPartIndex': currentPartIndex,
      'conversationId': conversationId,
      'createdAt': createdAt.toIso8601String(),
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// 从JSON创建
  factory ShortNovelMemory.fromJson(Map<String, dynamic> json) {
    return ShortNovelMemory(
      title: json['title'] as String,
      genres: List<String>.from(json['genres'] as List),
      background: json['background'] as String,
      otherRequirements: json['otherRequirements'] as String,
      targetReader: json['targetReader'] as String,
      targetWordCount: json['targetWordCount'] as int,
      worldView: json['worldView'] as String? ?? '',
      characterArcs: json['characterArcs'] as String? ?? '',
      detailedOutline: json['detailedOutline'] as String? ?? '',
      generatedParts: List<String>.from(json['generatedParts'] as List? ?? []),
      currentPartIndex: json['currentPartIndex'] as int? ?? 0,
      conversationId: json['conversationId'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  String toString() {
    return 'ShortNovelMemory(title: $title, progress: ${(progress * 100).toStringAsFixed(1)}%)';
  }
}

/// 短篇小说生成阶段枚举
enum ShortNovelGenerationStage {
  input('用户输入'),
  generatingWorldView('生成世界观'),
  editingWorldView('编辑世界观'),
  generatingCharacterArcs('生成角色脉络'),
  editingCharacterArcs('编辑角色脉络'),
  generatingOutline('生成大纲'),
  editingOutline('编辑大纲'),
  generatingContent('生成内容'),
  completed('完成'),
  error('错误');

  const ShortNovelGenerationStage(this.displayName);
  final String displayName;
}
